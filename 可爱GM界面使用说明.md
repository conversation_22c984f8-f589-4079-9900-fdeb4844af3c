# 🌸 可爱GM界面使用说明 🌸

## 概述
这是一个全新设计的可爱风格GM界面，采用粉色系配色方案，提供友好的用户体验和完整的GM功能。

## 开启方式
- **点击P1键位按钮**：在游戏的键位设置界面中，点击P1按钮即可打开/关闭GM界面
- **代码调用**：`CuteGMPanel.show()` 显示界面，`CuteGMPanel.hide()` 隐藏界面

## 界面特色
- 🎨 **可爱风格**：粉色系配色，圆角设计，温馨提示
- 🖱️ **友好交互**：按钮悬停效果，拖拽移动，动画反馈
- 📱 **标签页设计**：6个功能模块，清晰分类
- 💫 **动画效果**：淡入显示，状态栏闪烁提示

## 功能模块

### 🎮 角色管理
**基础属性设置：**
- 等级设置（推荐99）
- 金币设置（推荐999999999）
- 技能点设置（推荐999）
- 击杀点设置（推荐999999）

**快捷解锁功能：**
- 背包解锁：解锁所有背包格子
- 宠栏解锁：解锁所有宠物栏位
- 精灵槽解锁：解锁所有精灵槽位
- 特殊栏解锁：解锁特殊装备栏
- 关卡解锁：解锁所有游戏关卡
- 图鉴添加：添加所有怪物图鉴

**成就与其他：**
- 成就全亮：点亮所有成就
- 过检测：绕过游戏检测
- 宝珠满级：所有宝珠达到满级
- 悬赏全满：完成所有悬赏任务
- 四职业技能：获得所有职业技能

### ⚔️ 装备管理
**装备添加：**
- 输入装备ID和数量进行添加
- 一键装备：快速获得大量高级装备

**祝福系统：**
- 祝福界面1-6：不同类型的装备祝福
- 星灵王祝福：最高级别的装备祝福

**自定义装备：**
- 打开自定义装备界面（功能开发中）

### 🐾 宠物管理
- **一键宠物**：快速获得大量高级宠物
- **一键宠物装备**：为宠物配备装备
- **清空宠物**：清除所有宠物（谨慎使用）

### 💎 道具管理
**一键功能：**
- 一键道具：获得各种道具
- 一键消耗品：获得各种药品和消耗品
- 一键技能石：获得技能石
- 一键称号：获得所有称号
- 一键精灵：获得所有精灵

**清空功能：**
- 清空背包：清除背包所有物品
- 清空仓库：清除仓库所有物品
- 清空精灵：清除所有精灵
- 清空称号：清除所有称号

### 🎯 一键功能
**快速操作：**
- 一键角色满级：角色属性全部最大化
- 一键装备：快速获得装备
- 一键宠物：快速获得宠物
- 一键道具：快速获得道具
- 一键成就：解锁所有成就
- 一键解锁：解锁所有功能

### 🔧 工具箱
**代码查询：**
- 装备代码：查看所有装备ID和名称
- 道具代码：查看所有道具ID和名称
- 宠物代码：查看所有宠物ID和名称
- 精灵代码：查看所有精灵ID和名称
- 称号代码：查看所有称号ID和名称
- 宝石代码：查看所有宝石ID和名称
- 药品代码：查看所有药品ID和名称
- 宠装代码：查看所有宠物装备ID和名称

## 使用方法

### 开启界面
1. **点击P1键位按钮**：在游戏的键位设置界面中点击P1按钮即可打开/关闭GM界面
2. **代码调用**：`CuteGMPanel.show()` 显示界面，`CuteGMPanel.hide()` 隐藏界面
3. **测试按钮**：使用TestCuteGM类进行测试

### 操作流程
1. 进入游戏的键位设置界面
2. 点击P1按钮打开可爱GM界面
3. 选择相应的功能标签页
4. 根据需要设置参数或直接点击功能按钮
5. 查看状态栏的操作反馈
6. 再次点击P1按钮或点击关闭按钮关闭界面

## 使用技巧
1. **首次使用**：建议先使用"一键角色满级"和"一键解锁全部"
2. **代码查询**：在工具箱中查询需要的物品ID，然后在相应模块中添加
3. **谨慎操作**：清空功能会永久删除物品，使用前请确认
4. **界面拖拽**：可以拖拽标题栏移动界面位置
5. **状态提示**：注意查看底部状态栏的操作反馈

## 注意事项
- 本界面整合了原有GM.as的所有功能
- 所有操作都会有成功提示
- 部分功能可能需要重新进入游戏才能生效
- 建议在使用前备份存档

## 技术特性
- **单例模式**：确保只有一个界面实例
- **事件驱动**：响应式的用户交互
- **模块化设计**：功能分类清晰
- **兼容性好**：与原有GM系统完全兼容

## 开发信息
- 基于原有GM.as功能开发
- 采用ActionScript 3.0编写
- 支持Flash Player运行环境
- 可扩展的模块化架构

---
🌸 享受可爱的GM体验吧！ 🌸
