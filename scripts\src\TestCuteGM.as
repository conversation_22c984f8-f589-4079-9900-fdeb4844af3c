package src
{
   import flash.display.*;
   import flash.events.*;
   import src.CuteGMPanel;
   
   /**
    * 测试可爱GM界面的类
    */
   public class TestCuteGM extends MovieClip
   {
      public function TestCuteGM()
      {
         super();
         this.addEventListener(Event.ADDED_TO_STAGE, onAddedToStage);
      }
      
      private function onAddedToStage(e:Event):void
      {
         this.removeEventListener(Event.ADDED_TO_STAGE, onAddedToStage);
         
         // 创建测试按钮
         var testBtn:Sprite = new Sprite();
         testBtn.graphics.beginFill(0xFF69B4);
         testBtn.graphics.drawRoundRect(0, 0, 150, 40, 10, 10);
         testBtn.graphics.endFill();
         
         var btnText:TextField = new TextField();
         btnText.text = "测试可爱GM界面";
         btnText.x = 25;
         btnText.y = 12;
         btnText.textColor = 0xFFFFFF;
         btnText.selectable = false;
         testBtn.addChild(btnText);
         
         testBtn.x = 50;
         testBtn.y = 50;
         testBtn.buttonMode = true;
         testBtn.useHandCursor = true;
         
         testBtn.addEventListener(MouseEvent.CLICK, onTestClick);
         addChild(testBtn);
         
         // 添加说明文字
         var infoText:TextField = new TextField();
         infoText.text = "点击按钮测试可爱GM界面，或按P键打开/关闭界面";
         infoText.x = 50;
         infoText.y = 100;
         infoText.width = 400;
         infoText.textColor = 0x333333;
         infoText.selectable = false;
         addChild(infoText);
      }
      
      private function onTestClick(e:MouseEvent):void
      {
         // 显示可爱GM界面
         CuteGMPanel.show();
      }
   }
}
