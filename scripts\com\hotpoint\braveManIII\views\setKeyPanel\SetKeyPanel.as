package com.hotpoint.braveManIII.views.setKeyPanel
{
   import com.hotpoint.braveManIII.events.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import src.*;
   import src.tool.*;
   import src.CuteGMPanel;
   
   public class SetKeyPanel extends MovieClip
   {
      private static var _instance1:SetKeyPanel;
      
      private static var _instance:SetKeyPanel;
      
      private static var loadData:ClassLoader;
      
      private static var open_yn:Boolean;
      
      public static var ysArr:Array = [87,83,65,68,74,75,76,72,85,73,79,49,50,51,32,78];
      
      public static var ysArr2:Array = [38,40,37,39,97,98,99,100,101,102,107,103,104,105,96,34];
      
      private static var loadName:String = "Panel_key_v1.swf";
      
      private var keyNum:uint = 16;
      
      private var sure1:Array = [87,83,65,68,74,75,76,72,85,73,79,49,50,51,32,78];
      
      private var sure2:Array = [38,40,37,39,97,98,99,100,101,102,107,103,104,105,96,34];
      
      private var cchuArr:Array = [];
      
      private var p1ArrKeyArr:Array = [];
      
      private var p2ArrKeyArr:Array = [];
      
      private var clickState:Boolean;
      
      private var overBo:Boolean;
      
      private var oldCurrent:uint;
      
      private var targetId:uint = 1;
      
      public var p1_0:*;
      
      public var p1_1:*;
      
      public var p1_2:*;
      
      public var p1_3:*;
      
      public var p1_4:*;
      
      public var p1_5:*;
      
      public var p1_6:*;
      
      public var p1_7:*;
      
      public var p1_8:*;
      
      public var p1_9:*;
      
      public var p1_10:*;
      
      public var p1_11:*;
      
      public var p1_12:*;
      
      public var p1_13:*;
      
      public var p1_14:*;
      
      public var p1_15:*;
      
      public var p1_btn:*;
      
      public var p2_btn:*;
      
      public var sureMast:*;
      
      public function SetKeyPanel(param1:PrivateClass)
      {
         super();
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(param1:*) : *
      {
         var _loc2_:Class = loadData.getClass("com.hotpoint.braveManIII.views.setKeyPanel.SetKeyPanel") as Class;
         SetKeyPanel._instance = new _loc2_(new PrivateClass());
         SetKeyPanel._instance.initKey();
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(open_yn)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      public static function open(param1:Number = 1) : void
      {
         Main.stopXX = true;
         if(SetKeyPanel._instance == null)
         {
            Loading();
            open_yn = true;
            return;
         }
         _instance.setXY();
         Main._stage.addChild(SetKeyPanel._instance);
         _instance.openPlayerData(param1);
         _instance.visible = true;
         if(!Main.P1P2)
         {
            _instance.p2_btn.visible = false;
         }
      }
      
      public static function close() : void
      {
         Main.stopXX = false;
         if(SetKeyPanel._instance != null)
         {
            if(SetKeyPanel._instance.visible == true)
            {
               SetKeyPanel._instance.visible = false;
            }
         }
         else
         {
            open_yn = false;
         }
      }
      
      private function setXY() : void
      {
         var _loc1_:Number = Main._stage.stageWidth / 2;
         var _loc2_:Number = Main._stage.stageHeight / 2;
      }
      
      private function initKey() : *
      {
         this.initP1Frame();
         var _loc1_:Number = 0;
         while(_loc1_ < this.keyNum)
         {
            this.p1ArrKeyArr[_loc1_] = false;
            this.p2ArrKeyArr[_loc1_] = false;
            this["p1_" + _loc1_].id = _loc1_;
            this["p1_" + _loc1_].mouseChildren = false;
            this["p1_" + _loc1_].addEventListener(MouseEvent.MOUSE_OVER,this.overHandle);
            this["p1_" + _loc1_].addEventListener(MouseEvent.MOUSE_OUT,this.outHandle);
            this["p1_" + _loc1_].addEventListener(MouseEvent.CLICK,this.clickHandle);
            _loc1_++;
         }
         this.sureMast.visible = false;
         Main._stage.addEventListener(KeyboardEvent.KEY_DOWN,this.downHandle);
         addEventListener(BtnEvent.DO_CLOSE,this.btnCloseHandle);
         addEventListener(BtnEvent.DO_CHANGE,this.changeHandle);
         addEventListener(BtnEvent.DO_CLICK,this.btnClick);
         this["p1_btn"].addEventListener(MouseEvent.CLICK,this.p1p2_OPEN);
         if(Main.P1P2)
         {
            this["p2_btn"].addEventListener(MouseEvent.CLICK,this.p1p2_OPEN);
         }
         this["p2_btn"].mouseChildren = false;
         this["p1_btn"].mouseChildren = false;
      }
      
      public function p1p2_OPEN(param1:MouseEvent) : *
      {
         var _loc2_:int = int(param1.target.name.substr(1,1));
         TiaoShi.txtShow("p1p2_OPEN??" + _loc2_);
         TiaoShi.txtShow("按钮名称: " + param1.target.name);
         TiaoShi.txtShow("提取的数字: " + _loc2_);
         if(_loc2_ == 1)
         {
            TiaoShi.txtShow("准备打开可爱GM界面");
            // 点击P1按钮时打开可爱GM界面
            CuteGMPanel.toggle();
            TiaoShi.txtShow("已调用CuteGMPanel.toggle()");
            // 同时也执行原有的P1键位设置功能
            this.initP1Frame();
         }
         else
         {
            this.initP2Frame();
         }
      }
      
      private function initP1Frame(param1:* = null) : void
      {
         var _loc3_:* = undefined;
         TiaoShi.txtShow("p1键位:" + Main.player1._keyArr);
         var _loc2_:Number = 0;
         while(_loc2_ < this.keyNum)
         {
            this["p1_" + _loc2_].mouseChildren = false;
            this["p1_" + _loc2_].buttonMode = true;
            _loc3_ = this.XiuZengKey(1,_loc2_);
            if(Main.player1._keyArr[_loc2_] != -1)
            {
               this["p1_" + _loc2_].gotoAndStop("k" + _loc3_);
            }
            else
            {
               this["p1_" + _loc2_].gotoAndStop("kx");
            }
            _loc2_++;
         }
      }
      
      private function initP2Frame(param1:* = null) : void
      {
         var _loc3_:* = undefined;
         TiaoShi.txtShow("p2键位:" + Main.player2._keyArr);
         var _loc2_:Number = 0;
         while(_loc2_ < this.keyNum)
         {
            this["p1_" + _loc2_].mouseChildren = false;
            this["p1_" + _loc2_].buttonMode = true;
            _loc3_ = this.XiuZengKey(2,_loc2_);
            if(Main.player2._keyArr[_loc2_] != -1)
            {
               this["p1_" + _loc2_].gotoAndStop("k" + _loc3_);
            }
            else
            {
               this["p1_" + _loc2_].gotoAndStop("kx");
            }
            _loc2_++;
         }
      }
      
      public function XiuZengKey(param1:int, param2:int) : int
      {
         var _loc3_:* = Main.player1._keyArr;
         if(param1 == 2)
         {
            _loc3_ = Main.player2._keyArr;
         }
         var _loc4_:int = int(_loc3_[param2]);
         var _loc5_:Array = [219,221,220,188,190,191,192,32,186,222,37,38,39,40,48,49,50,51,52,53,54,55,56,57,187,189,96,97,98,99,100,101,102,103,104,105,107,109,111,106,110,34];
         var _loc6_:* = 0;
         while(_loc6_ < _loc5_.length)
         {
            if(_loc4_ == _loc5_[_loc6_])
            {
               return _loc4_;
            }
            _loc6_++;
         }
         if(_loc4_ >= 65 && _loc4_ <= 90)
         {
            return _loc4_;
         }
         if(param1 == 1)
         {
            Main.player1._keyArr[param2] = -1;
         }
         else
         {
            Main.player2._keyArr[param2] = -1;
         }
         return -1;
      }
      
      public function KeyYN(param1:int) : int
      {
         var _loc2_:Array = [219,221,220,188,190,191,192,32,186,222,37,38,39,40,48,49,50,51,52,53,54,55,56,57,187,189,96,97,98,99,100,101,102,103,104,105,107,109,111,106,110,34];
         var _loc3_:* = 0;
         while(_loc3_ < _loc2_.length)
         {
            if(param1 == _loc2_[_loc3_])
            {
               return param1;
            }
            _loc3_++;
         }
         if(param1 >= 65 && param1 <= 90)
         {
            return param1;
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"不支持该键位");
         return -1;
      }
      
      private function openPlayerData(param1:Number = 1) : void
      {
         if(param1 == 1)
         {
            this.p2_btn.gotoAndStop(4);
            this.updateFramep1();
         }
         if(param1 == 2)
         {
            this.p1_btn.gotoAndStop(4);
            this.updateFramep2();
         }
         this.sureMast.visible = false;
      }
      
      private function updateFramep1() : void
      {
         var _loc2_:* = undefined;
         var _loc1_:Number = 0;
         while(_loc1_ < this.keyNum)
         {
            if(Main.player1._keyArr[_loc1_] != -1)
            {
               _loc2_ = this.XiuZengKey(1,_loc1_);
               this["p1_" + _loc1_].gotoAndStop("k" + _loc2_);
            }
            else
            {
               this["p1_" + _loc1_].gotoAndStop("kx");
            }
            this.p1ArrKeyArr[_loc1_] = false;
            this["p1_" + _loc1_].key_mast.gotoAndStop(1);
            _loc1_++;
         }
      }
      
      private function updateFramep2() : void
      {
      }
      
      private function closeOk() : Boolean
      {
         var _loc1_:Number = 0;
         if(Main.player2 == null)
         {
            _loc1_ = 0;
            while(_loc1_ < this.keyNum)
            {
               if(Main.player1._keyArr[_loc1_] == -1)
               {
                  return false;
               }
               _loc1_++;
            }
         }
         else
         {
            _loc1_ = 0;
            while(_loc1_ < this.keyNum)
            {
               if(Main.player1._keyArr[_loc1_] == -1)
               {
                  return false;
               }
               if(Main.player2._keyArr[_loc1_] == -1)
               {
                  return false;
               }
               _loc1_++;
            }
         }
         return true;
      }
      
      private function saveDate() : void
      {
         if(Main.player2 == null)
         {
            this.sure1 = DeepCopyUtil.clone(Main.player1._keyArr);
         }
         else
         {
            this.sure1 = DeepCopyUtil.clone(Main.player1._keyArr);
            this.sure2 = DeepCopyUtil.clone(Main.player2._keyArr);
         }
         this.visible = false;
      }
      
      private function btnClick(param1:BtnEvent) : void
      {
         if(param1.target.id == 3)
         {
            if(this.closeOk())
            {
               this.saveDate();
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"键位设置成功,已保存");
            }
            else
            {
               this.sureMast.visible = true;
            }
         }
         if(param1.target.id == 4)
         {
            if(Main.player2 == null)
            {
               Main.player1._keyArr = DeepCopyUtil.clone(ysArr);
               this.updateFramep1();
            }
            else
            {
               if(this.targetId == 1)
               {
                  Main.player1._keyArr = DeepCopyUtil.clone(ysArr);
                  Main.player2._keyArr = DeepCopyUtil.clone(ysArr2);
                  this.updateFramep1();
               }
               if(this.targetId == 2)
               {
                  Main.player1._keyArr = DeepCopyUtil.clone(ysArr);
                  Main.player2._keyArr = DeepCopyUtil.clone(ysArr2);
                  this.updateFramep2();
               }
            }
         }
      }
      
      private function changeHandle(param1:BtnEvent) : void
      {
         this.targetId = param1.target.id;
         if(param1.target.id == 1)
         {
            this.updateFramep1();
         }
         if(param1.target.id == 2)
         {
            this.p1_btn.gotoAndStop(4);
            this.updateFramep2();
         }
      }
      
      private function btnCloseHandle(param1:BtnEvent) : void
      {
         if(param1.target.id == 1)
         {
            this.visible = false;
            close();
            if(Main.player2 == null)
            {
               Main.player1._keyArr = DeepCopyUtil.clone(this.sure1);
            }
            else
            {
               Main.player1._keyArr = DeepCopyUtil.clone(this.sure1);
               Main.player2._keyArr = DeepCopyUtil.clone(this.sure2);
            }
         }
         if(param1.target.id == 2)
         {
            this.sureMast.visible = false;
         }
         if(param1.target.id == 3)
         {
            this.sureMast.visible = false;
         }
      }
      
      private function downHandle(param1:KeyboardEvent) : void
      {
         var _loc3_:Number = 0;
         var _loc4_:* = undefined;
         var _loc2_:int = 0;
         while(_loc2_ < this.p1ArrKeyArr.length)
         {
            if(Main.player2 == null)
            {
               if(this.p1ArrKeyArr[_loc2_])
               {
                  _loc3_ = 0;
                  while(_loc3_ < this.p1ArrKeyArr.length)
                  {
                     if(Main.player1._keyArr[_loc3_] == param1.keyCode)
                     {
                        Main.player1._keyArr[_loc3_] = -1;
                        this["p1_" + _loc3_].gotoAndStop("kx");
                     }
                     _loc3_++;
                  }
                  Main.player1._keyArr[_loc2_] = this.KeyYN(param1.keyCode);
                  _loc4_ = Main.player1._keyArr[_loc2_];
                  if(_loc4_ == -1)
                  {
                     _loc4_ = 0;
                  }
                  this["p1_" + _loc2_].gotoAndStop("k" + _loc4_);
                  this["p1_" + _loc2_].key_mast.gotoAndStop(1);
                  this.p1ArrKeyArr[_loc2_] = false;
                  break;
               }
            }
            else if(this.targetId == 1)
            {
               if(this.p1ArrKeyArr[_loc2_])
               {
                  _loc3_ = 0;
                  while(_loc3_ < this.p1ArrKeyArr.length)
                  {
                     if(Main.player1._keyArr[_loc3_] == param1.keyCode)
                     {
                        Main.player1._keyArr[_loc3_] = -1;
                        this["p1_" + _loc3_].gotoAndStop("kx");
                     }
                     if(Main.player2._keyArr[_loc3_] == param1.keyCode)
                     {
                        Main.player2._keyArr[_loc3_] = -1;
                     }
                     _loc3_++;
                  }
                  Main.player1._keyArr[_loc2_] = this.KeyYN(param1.keyCode);
                  _loc4_ = Main.player1._keyArr[_loc2_];
                  if(_loc4_ == -1)
                  {
                     _loc4_ = 0;
                  }
                  this["p1_" + _loc2_].gotoAndStop("k" + _loc4_);
                  this["p1_" + _loc2_].key_mast.gotoAndStop(1);
                  this.p1ArrKeyArr[_loc2_] = false;
                  break;
               }
            }
            else if(this.targetId == 2)
            {
               if(this.p2ArrKeyArr[_loc2_])
               {
                  _loc3_ = 0;
                  while(_loc3_ < this.p2ArrKeyArr.length)
                  {
                     if(Main.player2._keyArr[_loc3_] == param1.keyCode)
                     {
                        Main.player2._keyArr[_loc3_] = -1;
                        this["p1_" + _loc3_].gotoAndStop("kx");
                     }
                     if(Main.player1._keyArr[_loc3_] == param1.keyCode)
                     {
                        Main.player1._keyArr[_loc3_] = -1;
                     }
                     _loc3_++;
                  }
                  Main.player2._keyArr[_loc2_] = this.KeyYN(param1.keyCode);
                  _loc4_ = Main.player2._keyArr[_loc2_];
                  if(_loc4_ == -1)
                  {
                     _loc4_ = 0;
                  }
                  this["p1_" + _loc2_].gotoAndStop("k" + _loc4_);
                  this["p1_" + _loc2_].key_mast.gotoAndStop(1);
                  this.p2ArrKeyArr[_loc2_] = false;
                  break;
               }
            }
            _loc2_++;
         }
      }
      
      private function clickHandle(param1:MouseEvent) : void
      {
         if(param1.target.key_mast)
         {
            if(Main.player2 == null)
            {
               this.p1Click(param1.target);
            }
            else if(this.targetId == 1)
            {
               this.p1Click(param1.target);
            }
            else if(this.targetId == 2)
            {
               this.p2Click(param1.target);
            }
         }
      }
      
      private function p1Click(param1:MovieClip) : void
      {
         var _loc2_:Number = 0;
         while(_loc2_ < this.keyNum)
         {
            this.p1ArrKeyArr[_loc2_] = false;
            this["p1_" + _loc2_].key_mast.gotoAndStop(1);
            if(Main.player1._keyArr[_loc2_] != -1)
            {
               this["p1_" + _loc2_].gotoAndStop("k" + Main.player1._keyArr[_loc2_]);
            }
            else
            {
               this["p1_" + _loc2_].gotoAndStop("kx");
            }
            _loc2_++;
         }
         param1.gotoAndStop("k0");
         param1.key_mast.gotoAndStop(3);
         this.p1ArrKeyArr[param1.id] = true;
      }
      
      private function p2Click(param1:MovieClip) : void
      {
         i = 0;
         while(i < this.keyNum)
         {
            this.p2ArrKeyArr[i] = false;
            this["p1_" + i].key_mast.gotoAndStop(1);
            if(Main.player2._keyArr[i] != -1)
            {
               this["p1_" + i].gotoAndStop("k" + Main.player2._keyArr[i]);
            }
            else
            {
               this["p1_" + i].gotoAndStop("kx");
            }
            ++i;
         }
         param1.gotoAndStop("k0");
         param1.key_mast.gotoAndStop(3);
         this.p2ArrKeyArr[param1.id] = true;
      }
      
      private function outHandle(param1:MouseEvent) : void
      {
         if(param1.target.key_mast)
         {
            if(Main.player2 == null)
            {
               this.p1out(param1.target);
            }
            else if(this.targetId == 1)
            {
               this.p1out(param1.target);
            }
            else if(this.targetId == 2)
            {
               this.p2out(param1.target);
            }
         }
      }
      
      private function p1out(param1:MovieClip) : void
      {
         if(this.p1ArrKeyArr[param1.id])
         {
            param1.key_mast.gotoAndStop(3);
         }
         else
         {
            param1.key_mast.gotoAndStop(1);
         }
      }
      
      private function p2out(param1:MovieClip) : void
      {
         if(this.p2ArrKeyArr[param1.id])
         {
            param1.key_mast.gotoAndStop(3);
         }
         else
         {
            param1.key_mast.gotoAndStop(1);
         }
      }
      
      private function overHandle(param1:MouseEvent) : void
      {
         if(Main.player2 == null)
         {
            this.p1over(param1.target);
         }
         else if(this.targetId == 1)
         {
            this.p1over(param1.target);
         }
         else if(this.targetId == 2)
         {
            this.p2over(param1.target);
         }
      }
      
      private function p1over(param1:MovieClip) : void
      {
         if(this.p1ArrKeyArr[param1.id])
         {
            param1.key_mast.gotoAndStop(3);
         }
         else
         {
            param1.key_mast.gotoAndStop(2);
         }
      }
      
      private function p2over(param1:MovieClip) : void
      {
         if(this.p2ArrKeyArr[param1.id])
         {
            param1.key_mast.gotoAndStop(3);
         }
         else
         {
            param1.key_mast.gotoAndStop(2);
         }
      }
   }
}

class PrivateClass
{
   public function PrivateClass()
   {
      super();
   }
}
